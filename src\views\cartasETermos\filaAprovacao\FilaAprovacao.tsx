import {
  CButton,
  CCard,
  CCardBody,
  CCol,
  CDataTable,
  CRow,
} from "@coreui/react";
import React, { useEffect, useState } from "react";
import { GET_DATA } from "src/api";
import { getURI } from "src/config/apiConfig";
import AprovacaoModal from "./partials/aprovacaoModal.tsx";
import EditTermoModal from "./partials/Termo/EditTermoModal.tsx";
import { ItemFilaAprovacaoType } from "../types/ItemFilaAprovacaoType.ts";
import { formatDate } from "src/reusable/helpers.js";
import LoadingComponent from "src/reusable/Loading.js";
import { postApiQueryFile } from "src/reusable/functions.js";
import { toast } from "react-toastify";
import ConteudoTermosModal from "./partials/Termo/ConteudoTermoModal.tsx";
import EditCartaDiluicaoModal from "./partials/CartaDiluicao/EditTermoCartaDiluicao.tsx";
import ConteudoCartaDiluicaoModal from "./partials/CartaDiluicao/ConteudoDiluicaoModal.tsx";
import EditCartaRetencaoModal from "./partials/CartaRetencao/EditCartaRetencaoModal.tsx";
import ConteudoCartaRetencaoModal from "./partials/CartaRetencao/ConteudoCartaRetencaoModal.tsx";

const FilaAprovacao = () => {
  const user = localStorage.getItem("user")
    ? JSON.parse(localStorage.getItem("user"))
    : null;

  const [isLoading, setIsLoading] = useState(false);
  const [isLoadingDocument, setIsLoadingDocument] = useState(false);
  const [filaAprovacao, setFilaAprovacao] = useState([]);

  const [showAprovacaoModal, setShowAprovacaoModal] = useState(false);
  const [showEditTermoModal, setShowEditTermoModal] = useState(false);
  const [showEditCartaDiluicaoModal, setShowEditCartaDiluicaoModal] =
    useState(false);
  const [itemSelecionado, setItemSelecionado] =
    useState<ItemFilaAprovacaoType>(null);
  const [statusAprovacao, setStatusAprovacao] = useState<string>("");
  const [showConteudoTermosModal, setShowConteudoTermosModal] = useState(false);
  const [showConteudoCartaDiluicaoModal, setShowConteudoCartaDiluicaoModal] =
    useState(false);
  const [showEditCartaRetencaoModal, setShowEditCartaRetencaoModal] =
    useState(false);
  const [showConteudoCartaRetencaoModal, setShowConteudoCartaRetencaoModal] =
    useState(false);

  const [isBackoffice, setIsBackoffice] = useState(false);
  const [isJuridico, setIsJuridico] = useState(false);

  const getFilaAprovacaoCartasETermos = async () => {
    setIsLoading(true);
    await GetData(null, "filaAprovacaoCartasETermos")
      .then((resultado: ItemFilaAprovacaoType[]) => {
        if (resultado) {
          setFilaAprovacao(resultado);
        } else {
        }
      })
      .catch((err) => {
        console.error(err);
      })
      .finally(() => {
        setIsLoading(false);
      });
  };

  const GetData = async (payload, endpoint = "") => {
    return new Promise(async (resolve, reject) => {
      try {
        const response = await GET_DATA(getURI(endpoint), payload, true);
        resolve(response);
      } catch (error) {
        reject(error);
      }
    });
  };

  const colums = [
    {
      key: "financiado",
      label: "Cliente",
    },
    {
      key: "nrContrato",
      label: "Contrato",
    },
    {
      key: "operador",
      label: "Operador",
    },
    {
      key: "createdAt",
      label: "Data Criação",
      filter: false,
    },
    {
      key: "status",
      label: "Status",
      filter: false,
    },
    {
      key: "statusJuridico",
      label: "Status Jurídico",
      filter: false,
    },
    {
      key: "tipoDescricao",
      label: "Tipo",
      filter: false,
    },
    {
      key: "actions",
      label: "Ações",
      filter: false,
    },
  ];
  const IconButton = ({
    icon,
    color,
    titulo,
    onClick,
    disabled = false,
    className = "",
  }) => {
    return (
      <CButton
        title={titulo}
        className={className}
        style={{
          border: "solid 1px",
          borderColor: color,
          color: color,
          padding: "2px 4px",
        }}
        onClick={onClick}
        disabled={disabled}
      >
        <i className={icon} />
      </CButton>
    );
  };
  const handleClickAprovar = (item: ItemFilaAprovacaoType, status: string) => {
    setShowAprovacaoModal(true);
    setItemSelecionado(item);
    setStatusAprovacao(status);
  };
  const handleCloseAprovacaoModal = async (updateData = false) => {
    setShowAprovacaoModal(false);
    setItemSelecionado(null);
    setStatusAprovacao("");
    if (updateData) await getFilaAprovacaoCartasETermos();
  };

  const handleClickBaixar = async (item: ItemFilaAprovacaoType) => {
    if (item.id === null || item.id === "") {
      toast.error("Não foi possível baixar o documento");
      return;
    }
    if (item.status !== "Aprovado") {
      toast.warning("Pedido não foi aprovado");
      return;
    }
    setIsLoadingDocument(true);
    try {
      let url = "";
      switch (item.tipo) {
        case 3:
          url = "postPedidoCartasEtermosGenerateTermoByPedido";
          break;
        case 2:
          url = "postPedidoCartasEtermosGenerateCartaDiluicaoByPedido";
          break;
        case 1:
          url = "postPedidoCartasEtermosGenerateCartaRetencaoByPedido";
          break;
        default:
          toast.error("Tipo de documento não encontrado");
          return;
      }
      const res = await postApiQueryFile(url, `idPedido=${item.id}`);
      if (res.ok) {
        const blob = await res.blob();
        const url = URL.createObjectURL(blob);
        const link = document.createElement("a");
        link.href = url;
        const time = new Date().getTime();
        link.download = `${time}.pdf`;
        document.body.appendChild(link);
        link.click();
        link.remove();
        URL.revokeObjectURL(url);
      } else {
        toast.error("Não foi possível baixar o documento");
      }
    } catch (err) {
      toast.error(err);
    }
    setIsLoadingDocument(false);
  };

  const handleClickEditar = (item: ItemFilaAprovacaoType) => {
    if (item.tipo === 3) {
      handleEditTermo(item);
    }
    if (item.tipo === 2) {
      handleEditCartaDiluicao(item);
    }
    if (item.tipo === 1) {
      handleEditCartaRetencao(item);
    }
  };

  const handleEditTermo = (item: ItemFilaAprovacaoType) => {
    setShowEditTermoModal(true);
    setItemSelecionado(item);
  };

  const handleEditCartaDiluicao = (item: ItemFilaAprovacaoType) => {
    setShowEditCartaDiluicaoModal(true);
    setItemSelecionado(item);
  };

  const handleEditCartaRetencao = (item: ItemFilaAprovacaoType) => {
    setShowEditCartaRetencaoModal(true);
    setItemSelecionado(item);
  };

  const handleCloseEditTermoModal = async (updateData = false) => {
    setShowEditTermoModal(false);
    setItemSelecionado(null);
    if (updateData) await getFilaAprovacaoCartasETermos();
  };

  const handleCloseEditCartaDiluicaoModal = async (updateData = false) => {
    setShowEditCartaDiluicaoModal(false);
    setItemSelecionado(null);
    if (updateData) await getFilaAprovacaoCartasETermos();
  };

  const handleCloseEditCartaRetencaoModal = async (updateData = false) => {
    setShowEditCartaRetencaoModal(false);
    setItemSelecionado(null);
    if (updateData) await getFilaAprovacaoCartasETermos();
  };

  const getFuncoesBackofficeAndJuridico = async () => {
    const responseBackoffice = await GET_DATA(
      getURI("getConfigByKey"),
      null,
      true,
      true,
      "funcoes_backoffice"
    );

    if (responseBackoffice !== null && responseBackoffice !== undefined) {
      const arrayBackoffice = JSON.parse(responseBackoffice);
      if (arrayBackoffice.length > 0) {
        const isBack = arrayBackoffice?.find((x) => x === user?.role?.id);
        if (isBack) {
          setIsBackoffice(true);
          return;
        }
      }
    }

    const responseJuridico = await GET_DATA(
      getURI("getConfigByKey"),
      null,
      true,
      true,
      "funcoes_juridicas"
    );

    if (responseJuridico !== null && responseJuridico !== undefined) {
      const arrayJuridico = JSON.parse(responseJuridico);
      if (arrayJuridico.length > 0) {
        const isJuridico = arrayJuridico?.find((x) => x === user?.role?.id);
        if (isJuridico) {
          setIsJuridico(true);
          return;
        }
      }
    }

    setIsBackoffice(false);
    setIsJuridico(false);
  };

  const handleDownloadPermission = (item: ItemFilaAprovacaoType) => {
    if (isBackoffice && item.status === "Aprovado") return true;

    if (
      isJuridico &&
      item.status === "Aprovado" &&
      item.statusJuridico === "Aprovado" &&
      item.tipo === 3
    )
      return true;

    if (item.status === "Aprovado" && item.tipo !== 3) return true;

    return false;
  };

  const handleChangeStatusApprovePermission = (item: ItemFilaAprovacaoType) => {
    if (isBackoffice && item.status !== "Aprovado") return true;
    if (
      isJuridico &&
      item.status === "Aprovado" &&
      item.tipo === 3 &&
      item.statusJuridico !== "Aprovado"
    )
      return true;
    return false;
  };

  const handleChangeStatusRejectPermission = (item: ItemFilaAprovacaoType) => {
    if (isBackoffice && item.status !== "Rejeitado") return true;
    if (
      isJuridico &&
      item.status === "Aprovado" &&
      item.tipo === 3 &&
      item.statusJuridico !== "Rejeitado"
    )
      return true;
    return false;
  };

  useEffect(() => {
    getFilaAprovacaoCartasETermos();
    getFuncoesBackofficeAndJuridico();
  }, []); // eslint-disable-line react-hooks/exhaustive-deps

  const renderActions = (item: ItemFilaAprovacaoType) => {
    return (
      <td>
        <IconButton
          className="ml-2"
          icon={"cil-check"}
          color={"blue"}
          titulo={"Aprovar"}
          onClick={() => handleClickAprovar(item, "Aprovado")}
          disabled={!handleChangeStatusApprovePermission(item)}
        />
        <IconButton
          className="ml-2"
          icon={"cil-pencil"}
          color={"orange"}
          titulo={"Editar"}
          onClick={() => handleClickEditar(item)}
        />
        <IconButton
          className="ml-2"
          icon={"cil-x-circle"}
          color={"red"}
          titulo={"Rejeitar"}
          onClick={() => handleClickAprovar(item, "Rejeitado")}
          disabled={!handleChangeStatusRejectPermission(item)}
        />
        <IconButton
          className="ml-2"
          icon={"cil-arrow-thick-bottom"}
          color={"blue"}
          titulo={
            item.status === "Aprovado"
              ? "Baixar Documento"
              : "Necessário Aprovação"
          }
          onClick={() => handleClickBaixar(item)}
          disabled={isLoadingDocument || !handleDownloadPermission(item)}
        />
      </td>
    );
  };

  return (
    <div>
      <CRow>
        <CCol>
          <h1>Fila de Aprovação de Documentos</h1>
        </CCol>
      </CRow>
      <CRow className={"mb-2"}>
        <CCol className="d-flex justify-content-start">
          <CButton
            color="info"
            onClick={() => {
              setShowConteudoTermosModal(true);
            }}
          >
            <i className="cil-settings" /> Tipos de Termos
          </CButton>
          <CButton
            color="info"
            onClick={() => {
              setShowConteudoCartaDiluicaoModal(true);
            }}
            className="ml-2"
          >
            <i className="cil-settings" /> Layout da Carta Diluição
          </CButton>
          <CButton
            color="info"
            onClick={() => {
              setShowConteudoCartaRetencaoModal(true);
            }}
            className="ml-2"
          >
            <i className="cil-settings" /> Layout da Carta Retenção
          </CButton>
        </CCol>
      </CRow>

      <CRow>
        <CCol>
          <CCard>
            <CCardBody>
              {isLoading ? (
                <div className="mt-2">
                  <LoadingComponent />
                </div>
              ) : (
                <>
                  <CDataTable
                    items={filaAprovacao}
                    fields={colums}
                    striped
                    hover
                    columnFilter
                    scopedSlots={{
                      createdAt: (item: ItemFilaAprovacaoType) => (
                        <td>{formatDate(item.createdAt)}</td>
                      ),
                      statusJuridico: (item: ItemFilaAprovacaoType) => (
                        <td>{item.statusJuridico ?? "---"}</td>
                      ),
                      actions: (item: ItemFilaAprovacaoType) =>
                        renderActions(item),
                    }}
                    itemsPerPage={15}
                    pagination
                    noItemsViewSlot={
                      <h5 className="text-center">
                        Sem resultados para exibir.
                      </h5>
                    }
                  />
                </>
              )}
            </CCardBody>
          </CCard>
        </CCol>
      </CRow>
      <CRow className={""}>
        <CCol className={"d-flex justify-content-end"}>
          <CButton color="danger" className={"mr-2"} onClick={() => {}}>
            Sair
          </CButton>
          <CButton color="success" onClick={() => {}}>
            Exportar Excel
          </CButton>
        </CCol>
      </CRow>
      {showAprovacaoModal && (
        <AprovacaoModal
          isOpen={showAprovacaoModal}
          item={itemSelecionado}
          statusAprovacao={statusAprovacao}
          onClose={(updateData) => {
            handleCloseAprovacaoModal(updateData);
          }}
          isJuridico={isJuridico}
        />
      )}
      {showEditTermoModal && (
        <EditTermoModal
          isOpen={showEditTermoModal}
          item={itemSelecionado}
          onClose={(updateData) => {
            handleCloseEditTermoModal(updateData);
          }}
          isJuridico={isJuridico}
        />
      )}
      {showEditCartaDiluicaoModal && (
        <EditCartaDiluicaoModal
          isOpen={showEditCartaDiluicaoModal}
          item={itemSelecionado}
          onClose={(updateData) => {
            handleCloseEditCartaDiluicaoModal(updateData);
          }}
        />
      )}

      {showConteudoTermosModal && (
        <ConteudoTermosModal
          isOpen={showConteudoTermosModal}
          onClose={() => setShowConteudoTermosModal(false)}
        />
      )}
      {showConteudoCartaDiluicaoModal && (
        <ConteudoCartaDiluicaoModal
          isOpen={showConteudoCartaDiluicaoModal}
          onClose={() => setShowConteudoCartaDiluicaoModal(false)}
        />
      )}

      {showEditCartaRetencaoModal && (
        <EditCartaRetencaoModal
          isOpen={showEditCartaRetencaoModal}
          item={itemSelecionado}
          onClose={(updateData) => {
            handleCloseEditCartaRetencaoModal(updateData);
          }}
        />
      )}

      {showConteudoCartaRetencaoModal && (
        <ConteudoCartaRetencaoModal
          isOpen={showConteudoCartaRetencaoModal}
          onClose={() => setShowConteudoCartaRetencaoModal(false)}
        />
      )}
    </div>
  );
};

export default FilaAprovacao;
