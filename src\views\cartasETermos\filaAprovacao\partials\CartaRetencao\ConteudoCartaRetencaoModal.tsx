import React, { use<PERSON><PERSON>back, useEffect, useState } from "react";

import {
  <PERSON>utton,
  CCard,
  CCardBody,
  CCol,
  CInput,
  CModal,
  CModalBody,
  CModalFooter,
  CModalHeader,
  CRow,
  CTextarea,
} from "@coreui/react";
import {
  getApi,
  putApi,
  postApi,
  getApiInline,
  deleteApi,
  deleteApiInline,
} from "src/reusable/functions";
import Select from "react-select";

import "react-quill/dist/quill.snow.css";

import { Editor } from "@tinymce/tinymce-react";
import LoadingComponent from "src/reusable/Loading";
import { toast } from "react-toastify";
import { getURI } from "src/config/apiConfig";

// Função auxiliar para buscar dados
const GetData = async (payload: any, endpoint: string = "") => {
  try {
    const response = await getApi(payload, endpoint);
    return response;
  } catch (error) {
    throw error;
  }
};

interface TipoCartaRetencao {
  id: string;
  nome: string;
  descricao: string;
  ativo: boolean;
  conteudo: ConteudoCartaRetencaoType[];
}

interface ConteudoCartaRetencaoType {
  nome: string;
  crm: string;
  grupoId: Number | null;
}

interface Props {
  isOpen: boolean;
  onClose: () => void;
}

const ConteudoCartaRetencaoModal = ({ isOpen, onClose }: Props) => {
  const [tiposCartaRetencao, setTiposCartaRetencao] = useState<TipoCartaRetencao[]>([]);

  const [selectedCartaRetencao, setSelectedCartaRetencao] = useState<TipoCartaRetencao | null>(null);
  const [selectedConteudoNome, setSelectedConteudoNome] = useState<string | null>(null);
  const [editingCartaRetencao, setEditingCartaRetencao] = useState<TipoCartaRetencao | null>(null);
  const [cabecalhoFile, setCabecalhoFile] = useState<File | null>(null);
  const [rodapeFile, setRodapeFile] = useState<File | null>(null);
  const [cabecalhoPreview, setCabecalhoPreview] = useState("");
  const [rodapePreview, setRodapePreview] = useState("");
  const [expandedCartaRetencao, setExpandedCartaRetencao] = useState<Set<string>>(new Set());

  const [loadingConteudo, setLoadingConteudo] = useState(false);
  const [conteudoCartaRetencao, setConteudoCartaRetencao] = useState("");
  const [nomeConteudo, setNomeConteudo] = useState("");
  const [isEditingConteudo, setIsEditingConteudo] = useState(false);

  const [grupos, setGrupos] = useState([]);
  const [selectedGrupo, setSelectedGrupo] = useState<any>(null);

  const handleClose = () => {
    onClose();
  };

  const toggleExpanded = (cartaRetencaoId: string) => {
    const newExpanded = new Set(expandedCartaRetencao);
    if (newExpanded.has(cartaRetencaoId)) {
      newExpanded.delete(cartaRetencaoId);
    } else {
      newExpanded.add(cartaRetencaoId);
    }
    setExpandedCartaRetencao(newExpanded);
  };

  const handleSelectCartaRetencao = async (cartaRetencao: TipoCartaRetencao) => {
    setSelectedCartaRetencao(cartaRetencao);
    setSelectedConteudoNome(null);
    setConteudoCartaRetencao("");
    setNomeConteudo("");
    setIsEditingConteudo(false);

    // Carregar conteúdos da carta de retenção
    const conteudos = await getConteudoCartaRetencaoByTipo(cartaRetencao.id);
    setSelectedCartaRetencao({ ...cartaRetencao, conteudo: conteudos });
  };

  const handleSelectConteudo = async (conteudo: ConteudoCartaRetencaoType) => {
    setSelectedConteudoNome(conteudo.nome);
    setNomeConteudo(conteudo.nome);
    setIsEditingConteudo(true);

    // Carregar conteúdo específico
    try {
      setLoadingConteudo(true);
      const response = await getApi(
        {
          tipoCartaRetencaoId: selectedCartaRetencao?.id,
          nome: conteudo.nome,
          crm: conteudo.crm,
          grupoId: conteudo.grupoId,
        },
        "getConteudoCartaRetencaoByTipo"
      );
      if (response?.conteudo) {
        setConteudoCartaRetencao(response.conteudo);
      }
      setLoadingConteudo(false);
    } catch (error) {
      console.error(error);
      setLoadingConteudo(false);
    }
  };

  const handleSaveConteudo = async () => {
    if (!selectedCartaRetencao || !nomeConteudo.trim()) {
      toast.error("Selecione uma carta de retenção e informe o nome do conteúdo");
      return;
    }

    try {
      const payload = {
        tipoCartaRetencaoId: selectedCartaRetencao.id,
        nome: nomeConteudo,
        conteudo: conteudoCartaRetencao,
        crm: selectedGrupo?.crm || "",
        grupoId: selectedGrupo?.id || null,
      };

      const response = await postApi(payload, "postConteudoCartaRetencao");
      if (response?.success) {
        toast.success("Conteúdo salvo com sucesso!");
        // Recarregar conteúdos
        const conteudos = await getConteudoCartaRetencaoByTipo(selectedCartaRetencao.id);
        setSelectedCartaRetencao({ ...selectedCartaRetencao, conteudo: conteudos });
      } else {
        toast.error("Erro ao salvar conteúdo");
      }
    } catch (error) {
      console.error(error);
      toast.error("Erro ao salvar conteúdo");
    }
  };

  const handleUpdateConteudo = async () => {
    if (!selectedCartaRetencao || !selectedConteudoNome) {
      toast.error("Selecione um conteúdo para atualizar");
      return;
    }

    try {
      const payload = {
        tipoCartaRetencaoId: selectedCartaRetencao.id,
        nome: selectedConteudoNome,
        conteudo: conteudoCartaRetencao,
        crm: selectedGrupo?.crm || "",
        grupoId: selectedGrupo?.id || null,
      };

      const response = await putApi(payload, "putConteudoCartaRetencao");
      if (response?.success) {
        toast.success("Conteúdo atualizado com sucesso!");
      } else {
        toast.error("Erro ao atualizar conteúdo");
      }
    } catch (error) {
      console.error(error);
      toast.error("Erro ao atualizar conteúdo");
    }
  };

  const handleDeleteConteudo = async (conteudo: ConteudoCartaRetencaoType) => {
    if (!selectedCartaRetencao) return;

    if (window.confirm("Tem certeza que deseja excluir este conteúdo?")) {
      try {
        const response = await deleteApi(
          {
            tipoCartaRetencaoId: selectedCartaRetencao.id,
            nome: conteudo.nome,
            crm: conteudo.crm,
            grupoId: conteudo.grupoId,
          },
          "deleteConteudoCartaRetencao"
        );

        if (response?.success) {
          toast.success("Conteúdo excluído com sucesso!");
          // Recarregar conteúdos
          const conteudos = await getConteudoCartaRetencaoByTipo(selectedCartaRetencao.id);
          setSelectedCartaRetencao({ ...selectedCartaRetencao, conteudo: conteudos });
          
          // Limpar seleção se foi o conteúdo selecionado que foi excluído
          if (selectedConteudoNome === conteudo.nome) {
            setSelectedConteudoNome(null);
            setConteudoCartaRetencao("");
            setNomeConteudo("");
            setIsEditingConteudo(false);
          }
        } else {
          toast.error("Erro ao excluir conteúdo");
        }
      } catch (error) {
        console.error(error);
        toast.error("Erro ao excluir conteúdo");
      }
    }
  };

  const handleNewConteudo = () => {
    setSelectedConteudoNome(null);
    setConteudoCartaRetencao("");
    setNomeConteudo("");
    setIsEditingConteudo(false);
  };

  const getTipoCartaRetencao = async () => {
    try {
      const response = await getApi({}, "getCartaRetencao");
      if (response && response.length > 0) {
        setTiposCartaRetencao(response);
      } else {
        setTiposCartaRetencao([]);
      }
    } catch (error) {
      console.error(error);
    }
  };

  const getConteudoCartaRetencaoByTipo = async (id: string) => {
    try {
      setLoadingConteudo(true);
      const response = await getApiInline(id, "getConteudoCartaRetencaoByTipo");
      setLoadingConteudo(false);
      return response;
    } catch (error) {
      console.error(error);
      setLoadingConteudo(false);
      return [];
    }
  };

  const getGrupos = async () => {
    try {
      const response = await getApi({}, "getGrupos");
      if (response && response.length > 0) {
        setGrupos(response);
      }
    } catch (error) {
      console.error(error);
    }
  };

  useEffect(() => {
    if (isOpen) {
      getTipoCartaRetencao();
      getGrupos();
    }
  }, [isOpen]);

  return (
    <CModal
      show={isOpen}
      onClose={handleClose}
      closeOnBackdrop={false}
      className="custom-modal modal-xxl"
      centered
    >
      <CModalHeader closeButton>
        <h5>Gerenciar Conteúdos de Cartas de Retenção</h5>
      </CModalHeader>
      <CModalBody>
        <CRow>
          <CCol md={4}>
            <CCard>
              <CCardBody>
                <h6>Tipos de Carta de Retenção</h6>
                {tiposCartaRetencao.map((cartaRetencao) => (
                  <div key={cartaRetencao.id} className="mb-2">
                    <div
                      className="d-flex justify-content-between align-items-center cursor-pointer p-2 border rounded"
                      onClick={() => handleSelectCartaRetencao(cartaRetencao)}
                      style={{
                        backgroundColor: selectedCartaRetencao?.id === cartaRetencao.id ? "#e3f2fd" : "white",
                        cursor: "pointer",
                      }}
                    >
                      <span>{cartaRetencao.nome}</span>
                    </div>
                    
                    {selectedCartaRetencao?.id === cartaRetencao.id && selectedCartaRetencao.conteudo && (
                      <div className="mt-2 ml-3">
                        {selectedCartaRetencao.conteudo.map((conteudo, index) => (
                          <div
                            key={index}
                            className="d-flex justify-content-between align-items-center p-1 border-bottom cursor-pointer"
                            onClick={() => handleSelectConteudo(conteudo)}
                            style={{
                              backgroundColor: selectedConteudoNome === conteudo.nome ? "#fff3e0" : "transparent",
                              cursor: "pointer",
                            }}
                          >
                            <small>{conteudo.nome}</small>
                            <CButton
                              size="sm"
                              color="danger"
                              variant="ghost"
                              onClick={(e) => {
                                e.stopPropagation();
                                handleDeleteConteudo(conteudo);
                              }}
                            >
                              ×
                            </CButton>
                          </div>
                        ))}
                        <CButton
                          size="sm"
                          color="primary"
                          variant="outline"
                          className="mt-2"
                          onClick={handleNewConteudo}
                        >
                          + Novo Conteúdo
                        </CButton>
                      </div>
                    )}
                  </div>
                ))}
              </CCardBody>
            </CCard>
          </CCol>
          
          <CCol md={8}>
            {selectedCartaRetencao && (
              <CCard>
                <CCardBody>
                  <div className="d-flex justify-content-between align-items-center mb-3">
                    <h6>
                      {isEditingConteudo ? "Editando" : "Novo"} Conteúdo - {selectedCartaRetencao.nome}
                    </h6>
                  </div>
                  
                  <div className="row mb-3">
                    <div className="col-md-6">
                      <label>Nome do Conteúdo:</label>
                      <CInput
                        value={nomeConteudo}
                        onChange={(e) => setNomeConteudo(e.target.value)}
                        placeholder="Nome do conteúdo"
                      />
                    </div>
                    <div className="col-md-6">
                      <label>Grupo:</label>
                      <Select
                        options={grupos}
                        getOptionLabel={(option) => option?.nome}
                        getOptionValue={(option) => option?.id}
                        value={selectedGrupo}
                        onChange={setSelectedGrupo}
                        placeholder="Selecione um grupo (opcional)"
                        isClearable
                      />
                    </div>
                  </div>

                  {loadingConteudo ? (
                    <LoadingComponent />
                  ) : (
                    <div>
                      <label>Conteúdo:</label>
                      <Editor
                        apiKey="your-tinymce-api-key"
                        value={conteudoCartaRetencao}
                        onEditorChange={(content) => setConteudoCartaRetencao(content)}
                        init={{
                          height: 400,
                          menubar: false,
                          plugins: [
                            'advlist autolink lists link image charmap print preview anchor',
                            'searchreplace visualblocks code fullscreen',
                            'insertdatetime media table paste code help wordcount'
                          ],
                          toolbar:
                            'undo redo | formatselect | bold italic backcolor | \
                            alignleft aligncenter alignright alignjustify | \
                            bullist numlist outdent indent | removeformat | help'
                        }}
                      />
                    </div>
                  )}

                  <div className="mt-3">
                    {isEditingConteudo ? (
                      <CButton color="success" onClick={handleUpdateConteudo}>
                        Atualizar Conteúdo
                      </CButton>
                    ) : (
                      <CButton color="primary" onClick={handleSaveConteudo}>
                        Salvar Conteúdo
                      </CButton>
                    )}
                  </div>
                </CCardBody>
              </CCard>
            )}
          </CCol>
        </CRow>
      </CModalBody>
      <CModalFooter>
        <CButton color="secondary" onClick={handleClose}>
          Fechar
        </CButton>
      </CModalFooter>
    </CModal>
  );
};

export default ConteudoCartaRetencaoModal;
