import React, { useRef, useState } from 'react';
import { <PERSON>utton, CCard, CCardBody, CCardHeader, CCol, CRow } from '@coreui/react';
import Editor, { htmlToMarkdown, markdownToHtml } from '../reusable/Editor';

const EditorExample = () => {
  const editorRef = useRef(null);
  const [content, setContent] = useState('');
  const [markdownContent, setMarkdownContent] = useState('');

  const handleTextChange = () => {
    if (editorRef.current) {
      setContent(editorRef.current.getHTML());
    }
  };

  const insertSampleTable = () => {
    if (editorRef.current) {
      editorRef.current.insertTable(4, 3);
    }
  };

  const insertSampleHTML = () => {
    const sampleHTML = `
      <h2>Exemplo de HTML</h2>
      <p>Este é um parágrafo com <strong>texto em negrito</strong> e <em>texto em itálico</em>.</p>
      <ul>
        <li>Item 1 da lista</li>
        <li>Item 2 da lista</li>
        <li>Item 3 da lista</li>
      </ul>
    `;
    if (editorRef.current) {
      editorRef.current.insertHTML(sampleHTML);
    }
  };

  const convertToMarkdown = () => {
    if (editorRef.current) {
      const markdown = editorRef.current.getMarkdown();
      setMarkdownContent(markdown);
    }
  };

  const loadFromMarkdown = () => {
    const sampleMarkdown = `# Título Principal

## Subtítulo

Este é um parágrafo com **texto em negrito** e *texto em itálico*.

### Lista de itens:
- Item 1
- Item 2
- Item 3

### Código:
\`\`\`javascript
function exemplo() {
  console.log("Olá mundo!");
}
\`\`\`

### Link:
[Visite o Google](https://www.google.com)

### Citação:
> Esta é uma citação importante.
`;
    
    if (editorRef.current) {
      editorRef.current.setMarkdown(sampleMarkdown);
    }
  };

  const clearEditor = () => {
    if (editorRef.current) {
      editorRef.current.setHTML('');
      setContent('');
      setMarkdownContent('');
    }
  };

  const getPlainText = () => {
    if (editorRef.current) {
      const plainText = editorRef.current.getPlainText();
      alert(`Texto simples:\n${plainText}`);
    }
  };

  return (
    <CRow>
      <CCol xs={12}>
        <CCard>
          <CCardHeader>
            <h4>Editor Quill Completo</h4>
            <p className="text-muted">
              Editor com suporte completo para formatação de texto, tabelas, HTML e Markdown
            </p>
          </CCardHeader>
          <CCardBody>
            {/* Botões de ação */}
            <div className="mb-3">
              <CButton 
                color="primary" 
                size="sm" 
                className="me-2 mb-2"
                onClick={insertSampleTable}
              >
                Inserir Tabela
              </CButton>
              <CButton 
                color="secondary" 
                size="sm" 
                className="me-2 mb-2"
                onClick={insertSampleHTML}
              >
                Inserir HTML
              </CButton>
              <CButton 
                color="info" 
                size="sm" 
                className="me-2 mb-2"
                onClick={loadFromMarkdown}
              >
                Carregar Markdown
              </CButton>
              <CButton 
                color="success" 
                size="sm" 
                className="me-2 mb-2"
                onClick={convertToMarkdown}
              >
                Converter para Markdown
              </CButton>
              <CButton 
                color="warning" 
                size="sm" 
                className="me-2 mb-2"
                onClick={getPlainText}
              >
                Obter Texto Simples
              </CButton>
              <CButton 
                color="danger" 
                size="sm" 
                className="mb-2"
                onClick={clearEditor}
              >
                Limpar
              </CButton>
            </div>

            {/* Editor */}
            <div style={{ minHeight: '300px' }}>
              <Editor
                ref={editorRef}
                onTextChange={handleTextChange}
                defaultValue=""
                readOnly={false}
              />
            </div>

            {/* Visualização do conteúdo */}
            {content && (
              <div className="mt-4">
                <h5>Conteúdo HTML:</h5>
                <pre className="bg-light p-3" style={{ fontSize: '12px', maxHeight: '200px', overflow: 'auto' }}>
                  {content}
                </pre>
              </div>
            )}

            {markdownContent && (
              <div className="mt-4">
                <h5>Conteúdo Markdown:</h5>
                <pre className="bg-light p-3" style={{ fontSize: '12px', maxHeight: '200px', overflow: 'auto' }}>
                  {markdownContent}
                </pre>
              </div>
            )}
          </CCardBody>
        </CCard>
      </CCol>
    </CRow>
  );
};

export default EditorExample;
