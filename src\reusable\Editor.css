/* Estilos personalizados para o Editor Quill */

/* Container principal do editor */
.ql-container {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', sans-serif;
  font-size: 14px;
  line-height: 1.6;
}

/* Toolbar personalizada */
.ql-toolbar {
  border: 1px solid #e0e6ed;
  border-bottom: none;
  background-color: #f8f9fa;
  border-radius: 6px 6px 0 0;
  padding: 8px;
}

/* Editor content area */
.ql-editor {
  border: 1px solid #e0e6ed;
  border-top: none;
  border-radius: 0 0 6px 6px;
  min-height: 200px;
  padding: 16px;
  background-color: #ffffff;
}

/* Placeholder text */
.ql-editor.ql-blank::before {
  color: #6c757d;
  font-style: italic;
  content: attr(data-placeholder);
}

/* <PERSON><PERSON><PERSON>es da toolbar */
.ql-toolbar .ql-formats {
  margin-right: 15px;
}

.ql-toolbar button {
  border-radius: 3px;
  margin: 1px;
  padding: 5px;
  transition: background-color 0.2s ease;
}

.ql-toolbar button:hover {
  background-color: #e9ecef;
}

.ql-toolbar button.ql-active {
  background-color: #007bff;
  color: white;
}

/* Dropdowns da toolbar */
.ql-toolbar .ql-picker {
  border-radius: 3px;
}

.ql-toolbar .ql-picker-label {
  border-radius: 3px;
  padding: 5px 8px;
  transition: background-color 0.2s ease;
}

.ql-toolbar .ql-picker-label:hover {
  background-color: #e9ecef;
}

.ql-toolbar .ql-picker-options {
  border-radius: 3px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
  border: 1px solid #e0e6ed;
}

/* Estilos para tabelas */
.ql-editor table {
  border-collapse: collapse;
  width: 100%;
  margin: 10px 0;
}

.ql-editor table td,
.ql-editor table th {
  border: 1px solid #e0e6ed;
  padding: 8px 12px;
  text-align: left;
}

.ql-editor table th {
  background-color: #f8f9fa;
  font-weight: 600;
}

.ql-editor table tr:nth-child(even) {
  background-color: #f8f9fa;
}

.ql-editor table tr:hover {
  background-color: #e9ecef;
}

/* Estilos para blocos de código */
.ql-editor pre {
  background-color: #f8f9fa;
  border: 1px solid #e0e6ed;
  border-radius: 4px;
  padding: 12px;
  margin: 10px 0;
  overflow-x: auto;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 13px;
  line-height: 1.4;
}

.ql-editor code {
  background-color: #f8f9fa;
  border: 1px solid #e0e6ed;
  border-radius: 3px;
  padding: 2px 4px;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 13px;
}

/* Estilos para citações */
.ql-editor blockquote {
  border-left: 4px solid #007bff;
  margin: 16px 0;
  padding: 8px 16px;
  background-color: #f8f9fa;
  font-style: italic;
  color: #495057;
}

/* Estilos para listas */
.ql-editor ul,
.ql-editor ol {
  margin: 10px 0;
  padding-left: 30px;
}

.ql-editor li {
  margin: 5px 0;
  line-height: 1.6;
}

/* Estilos para links */
.ql-editor a {
  color: #007bff;
  text-decoration: none;
  border-bottom: 1px dotted #007bff;
  transition: color 0.2s ease;
}

.ql-editor a:hover {
  color: #0056b3;
  border-bottom-style: solid;
}

/* Estilos para imagens */
.ql-editor img {
  max-width: 100%;
  height: auto;
  border-radius: 4px;
  margin: 10px 0;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

/* Estilos para headers */
.ql-editor h1,
.ql-editor h2,
.ql-editor h3,
.ql-editor h4,
.ql-editor h5,
.ql-editor h6 {
  margin: 20px 0 10px 0;
  font-weight: 600;
  line-height: 1.3;
}

.ql-editor h1 {
  font-size: 2em;
  border-bottom: 2px solid #e0e6ed;
  padding-bottom: 8px;
}

.ql-editor h2 {
  font-size: 1.5em;
  border-bottom: 1px solid #e0e6ed;
  padding-bottom: 4px;
}

.ql-editor h3 {
  font-size: 1.25em;
}

.ql-editor h4 {
  font-size: 1.1em;
}

.ql-editor h5,
.ql-editor h6 {
  font-size: 1em;
}

/* Estilos para o modo somente leitura */
.ql-container.ql-disabled .ql-editor {
  background-color: #f8f9fa;
  color: #6c757d;
  cursor: not-allowed;
}

/* Responsividade */
@media (max-width: 768px) {
  .ql-toolbar {
    padding: 6px;
  }
  
  .ql-toolbar .ql-formats {
    margin-right: 8px;
  }
  
  .ql-editor {
    padding: 12px;
    min-height: 150px;
  }
}

/* Animações suaves */
.ql-editor * {
  transition: all 0.2s ease;
}

/* Estilos para seleção de texto */
.ql-editor ::selection {
  background-color: rgba(0, 123, 255, 0.2);
}

/* Estilos para foco */
.ql-container.ql-focused .ql-editor {
  border-color: #007bff;
  box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}
