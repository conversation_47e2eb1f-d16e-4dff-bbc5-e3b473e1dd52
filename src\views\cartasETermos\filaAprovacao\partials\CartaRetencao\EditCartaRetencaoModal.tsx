import React, { useCallback, useEffect, useState } from "react";
import {
  CButton,
  CInput,
  CModal,
  CModalBody,
  CModalFooter,
  CModalHeader,
} from "@coreui/react";
import {
  getApi,
  getApiInline,
  postApiQueryFile,
  putApi,
} from "src/reusable/functions";
import CardLoading from "src/reusable/CardLoading";
import { toast } from "react-toastify";
import { ItemFilaAprovacaoType } from "../../../types/ItemFilaAprovacaoType.ts";
import {
  CartaRetencaoInfosType,
  TipoAcordoCartaRetencao,
} from "../../../types/CartaRetencaoTypes.ts";
import Select from "react-select";

interface Props {
  isOpen: boolean;
  item: ItemFilaAprovacaoType | null;
  onClose: (updateData?: boolean) => void;
}

const EditCartaRetencaoModal = ({ isOpen, onClose, item }: Props) => {
  const handleClose = () => {
    onClose();
  };

  const [loading, setLoading] = useState(false);
  const [cartaRetencaoInfos, setCartaRetencaoInfos] =
    useState<CartaRetencaoInfosType | null>(null);
  const [tiposCartaRetencao, setTiposCartaRetencao] = useState([]);
  const [tipoCartaRetencaoSelected, setTipoCartaRetencaoSelected] =
    useState("");

  // Estados do formulário
  const [nomeCliente, setNomeCliente] = useState("");
  const [grupoCota, setGrupoCota] = useState("");
  const [administradoraConsorcio, setAdministradoraConsorcio] = useState("");
  const [tipoAcordo, setTipoAcordo] = useState("");
  const [parcelaProrrogada, setParcelaProrrogada] = useState("");
  const [parcelaRateio, setParcelaRateio] = useState("");
  const [parcelasARatear, setParcelasARatear] = useState("");
  const [bemAtual, setBemAtual] = useState("");
  const [valorBemAtual, setValorBemAtual] = useState("");
  const [novoBem, setNovoBem] = useState("");
  const [valorNovoBem, setValorNovoBem] = useState("");
  const [valorParcela, setValorParcela] = useState("");
  const [dataProcesso, setDataProcesso] = useState("");

  const loadCartaRetencaoInfos = useCallback(async () => {
    if (!item?.id) return;

    setLoading(true);
    try {
      const response = await getApiInline(item.id, "getCartaRetencaoInfos");
      if (response?.success && response?.data) {
        const data = response.data;
        setCartaRetencaoInfos(data);
        setTipoCartaRetencaoSelected(data.tipoCartaRetencaoId || "");
        setNomeCliente(data.nomeCliente || "");
        setGrupoCota(data.grupoCota || "");
        setAdministradoraConsorcio(data.administradoraConsorcio || "");
        setTipoAcordo(data.tipoAcordo || "");
        setParcelaProrrogada(data.parcelaProrrogada || "");
        setParcelaRateio(data.parcelaRateio || "");
        setParcelasARatear(data.parcelasARatear || "");
        setBemAtual(data.bemAtual || "");
        setValorBemAtual(data.valorBemAtual?.toString() || "");
        setNovoBem(data.novoBem || "");
        setValorNovoBem(data.valorNovoBem?.toString() || "");
        setValorParcela(data.valorParcela?.toString() || "");
        setDataProcesso(data.dataProcesso || "");
      }
    } catch (error) {
      console.error(
        "Erro ao carregar informações da carta de retenção:",
        error
      );
      toast.error("Erro ao carregar informações da carta de retenção");
    }
    setLoading(false);
  }, [item?.id]);

  const loadTiposCartaRetencao = useCallback(async () => {
    try {
      const response = await getApi({}, "getCartaRetencao");
      if (response && response.length > 0) {
        setTiposCartaRetencao(response);
      }
    } catch (error) {
      console.error("Erro ao carregar tipos de carta de retenção:", error);
    }
  }, []);

  useEffect(() => {
    if (isOpen && item) {
      loadTiposCartaRetencao();
      loadCartaRetencaoInfos();
    }
  }, [isOpen, item, loadTiposCartaRetencao, loadCartaRetencaoInfos]);

  const [previewLoading, setPreviewLoading] = useState(false);
  const [previewUrl, setPreviewUrl] = useState(null);
  const [showPreview, setShowPreview] = useState(false);

  const handlePreview = async () => {
    setPreviewLoading(true);
    try {
      const previewResponse = await postApiQueryFile(
        "postPedidoCartasEtermosGenerateCartaRetencaoPreview",
        "",
        {
          crm: item.crm,
          idGrupo: item.idGrupo,
          tipoCartaRetencaoId: tipoCartaRetencaoSelected,
          nomeCliente: nomeCliente,
          grupoCota: grupoCota,
          administradoraConsorcio: administradoraConsorcio,
          tipoAcordo: tipoAcordo,
          parcelaProrrogada: parcelaProrrogada,
          parcelaRateio: parcelaRateio,
          parcelasARatear: parcelasARatear,
          bemAtual: bemAtual,
          valorBemAtual: parseFloat(valorBemAtual) || 0,
          novoBem: novoBem,
          valorNovoBem: parseFloat(valorNovoBem) || 0,
          valorParcela: parseFloat(valorParcela) || 0,
          dataProcesso: dataProcesso,
        }
      );

      if (previewResponse.ok) {
        const blob = await previewResponse.blob();
        if (blob.type === "application/pdf" || blob.size > 0) {
          const url = URL.createObjectURL(blob);
          setPreviewUrl(url);
          setShowPreview(true);
        } else {
          toast.error("Não foi possível gerar o preview do documento");
        }
      } else {
        toast.error("Erro ao gerar preview do documento");
      }
    } catch (error) {
      console.error("Erro ao gerar preview:", error);
      toast.error("Erro ao gerar preview do documento");
    }
    setPreviewLoading(false);
  };

  const handleClosePreview = () => {
    setShowPreview(false);
    if (previewUrl) {
      URL.revokeObjectURL(previewUrl);
      setPreviewUrl(null);
    }
  };

  const [saveLoading, setSaveLoading] = useState(false);

  const handleSave = async () => {
    setSaveLoading(true);
    try {
      const response = await putApi(
        {
          id: cartaRetencaoInfos?.id,
          tipoCartaRetencaoId: tipoCartaRetencaoSelected,
          nomeCliente: nomeCliente,
          grupoCota: grupoCota,
          administradoraConsorcio: administradoraConsorcio,
          tipoAcordo: tipoAcordo,
          parcelaProrrogada: parcelaProrrogada,
          parcelaRateio: parcelaRateio,
          parcelasARatear: parcelasARatear,
          bemAtual: bemAtual,
          valorBemAtual: parseFloat(valorBemAtual) || 0,
          novoBem: novoBem,
          valorNovoBem: parseFloat(valorNovoBem) || 0,
          valorParcela: parseFloat(valorParcela) || 0,
          dataProcesso: dataProcesso,
        },
        "putCartaRetencaoInfos"
      );

      if (response?.success) {
        toast.success("Carta de retenção atualizada com sucesso!");
        onClose(true);
      } else {
        toast.error("Erro ao atualizar carta de retenção");
      }
    } catch (error) {
      console.error("Erro ao salvar:", error);
      toast.error("Erro ao atualizar carta de retenção");
    }
    setSaveLoading(false);
  };

  const tiposAcordo = [
    {
      value: TipoAcordoCartaRetencao.RATEIO_100,
      label: TipoAcordoCartaRetencao.RATEIO_100,
    },
    {
      value: TipoAcordoCartaRetencao.PRORROGACAO,
      label: TipoAcordoCartaRetencao.PRORROGACAO,
    },
    {
      value: TipoAcordoCartaRetencao.PRORROGACAO_RATEIO_50,
      label: TipoAcordoCartaRetencao.PRORROGACAO_RATEIO_50,
    },
    {
      value: TipoAcordoCartaRetencao.PRORROGACAO_TROCA_BEM,
      label: TipoAcordoCartaRetencao.PRORROGACAO_TROCA_BEM,
    },
    {
      value: TipoAcordoCartaRetencao.RATEIO_50,
      label: TipoAcordoCartaRetencao.RATEIO_50,
    },
    {
      value: TipoAcordoCartaRetencao.TROCA_BEM,
      label: TipoAcordoCartaRetencao.TROCA_BEM,
    },
  ];

  return (
    <CModal
      show={isOpen}
      onClose={handleClose}
      closeOnBackdrop={false}
      className="custom-modal modal-xxl"
      centered
    >
      <CModalHeader closeButton>
        <h5>Editar Carta de Retenção</h5>
      </CModalHeader>
      <CModalBody>
        {loading ? (
          <CardLoading Title={""} Msg={""} />
        ) : (
          <>
            <div className="row mb-3">
              <div className="col-md-6">
                <label>Tipo de Carta de Retenção:</label>
                <Select
                  options={tiposCartaRetencao}
                  getOptionLabel={(option) => option?.nome}
                  getOptionValue={(option) => option?.id}
                  value={tiposCartaRetencao.find(
                    (t) => t.id === tipoCartaRetencaoSelected
                  )}
                  onChange={(option) =>
                    setTipoCartaRetencaoSelected(option?.id || "")
                  }
                  placeholder="Selecione"
                />
              </div>
              <div className="col-md-6">
                <label>Tipo de Acordo:</label>
                <Select
                  options={tiposAcordo}
                  value={tiposAcordo.find((t) => t.value === tipoAcordo)}
                  onChange={(option) => setTipoAcordo(option?.value || "")}
                  placeholder="Selecione o tipo de acordo"
                />
              </div>
            </div>

            <div className="row mb-3">
              <div className="col-md-4">
                <label>Nome do Cliente:</label>
                <CInput
                  value={nomeCliente}
                  onChange={(e) => setNomeCliente(e.currentTarget.value)}
                  placeholder="Nome do cliente"
                />
              </div>
              <div className="col-md-4">
                <label>Grupo e Cota:</label>
                <CInput
                  value={grupoCota}
                  onChange={(e) => setGrupoCota(e.currentTarget.value)}
                  placeholder="Grupo e Cota"
                />
              </div>
              <div className="col-md-4">
                <label>Administradora de Consórcio:</label>
                <CInput
                  value={administradoraConsorcio}
                  onChange={(e) =>
                    setAdministradoraConsorcio(e.currentTarget.value)
                  }
                  placeholder="Administradora de Consórcio"
                />
              </div>
            </div>

            <div className="row mb-3">
              <div className="col-md-4">
                <label>Parcela Prorrogada:</label>
                <CInput
                  value={parcelaProrrogada}
                  onChange={(e) => setParcelaProrrogada(e.currentTarget.value)}
                />
              </div>
              <div className="col-md-4">
                <label>Parcela Rateio:</label>
                <CInput
                  value={parcelaRateio}
                  onChange={(e) => setParcelaRateio(e.currentTarget.value)}
                />
              </div>
              <div className="col-md-4">
                <label>Parcelas a Ratear:</label>
                <CInput
                  value={parcelasARatear}
                  onChange={(e) => setParcelasARatear(e.currentTarget.value)}
                />
              </div>
            </div>

            <div className="row mb-3">
              <div className="col-md-3">
                <label>Bem Atual:</label>
                <CInput
                  value={bemAtual}
                  onChange={(e) => setBemAtual(e.currentTarget.value)}
                />
              </div>
              <div className="col-md-3">
                <label>Valor Bem Atual:</label>
                <CInput
                  value={valorBemAtual}
                  onChange={(e) => setValorBemAtual(e.currentTarget.value)}
                  type="number"
                  step="0.01"
                />
              </div>
              <div className="col-md-3">
                <label>Novo Bem:</label>
                <CInput
                  value={novoBem}
                  onChange={(e) => setNovoBem(e.currentTarget.value)}
                />
              </div>
              <div className="col-md-3">
                <label>Valor Novo Bem:</label>
                <CInput
                  value={valorNovoBem}
                  onChange={(e) => setValorNovoBem(e.currentTarget.value)}
                  type="number"
                  step="0.01"
                />
              </div>
            </div>

            <div className="row mb-3">
              <div className="col-md-6">
                <label>Valor Parcela:</label>
                <CInput
                  value={valorParcela}
                  onChange={(e) => setValorParcela(e.currentTarget.value)}
                  type="number"
                  step="0.01"
                />
              </div>
              <div className="col-md-6">
                <label>Data do Processo:</label>
                <CInput
                  value={dataProcesso}
                  onChange={(e) => setDataProcesso(e.currentTarget.value)}
                  type="date"
                />
              </div>
            </div>
          </>
        )}
      </CModalBody>
      <CModalFooter>
        <CButton color="secondary" onClick={handleClose}>
          Cancelar
        </CButton>
        <CButton
          color="warning"
          onClick={handlePreview}
          disabled={previewLoading}
        >
          {previewLoading ? "Gerando..." : "Preview"}
        </CButton>
        <CButton color="success" onClick={handleSave} disabled={saveLoading}>
          {saveLoading ? "Salvando..." : "Salvar"}
        </CButton>
      </CModalFooter>

      {showPreview && (
        <CModal
          show={showPreview}
          onClose={handleClosePreview}
          closeOnBackdrop={false}
          className="preview-modal"
          centered
        >
          <CModalHeader closeButton>
            <h5>Preview da Carta de Retenção</h5>
          </CModalHeader>
          <CModalBody style={{ padding: "0", height: "80vh" }}>
            {previewUrl && (
              <iframe
                src={previewUrl}
                style={{
                  width: "100%",
                  height: "100%",
                  border: "none",
                }}
                title="Preview da Carta de Retenção"
              />
            )}
          </CModalBody>
          <CModalFooter>
            <CButton color="secondary" onClick={handleClosePreview}>
              Fechar Preview
            </CButton>
          </CModalFooter>
        </CModal>
      )}
    </CModal>
  );
};

export default EditCartaRetencaoModal;
