import React, { forwardRef, useEffect, useLayoutEffect, useRef } from "react";
import { Quill } from "react-quill";
import "quill-better-table/dist/quill-better-table.css";
import QuillBetterTable from "quill-better-table";

import "react-quill/dist/quill.snow.css";
import "./Editor.css";

// Registrar módulos do Quill
Quill.register("modules/better-table", QuillBetterTable);

// Configuração da toolbar completa
const toolbarOptions = [
  // Formatação de texto
  [{ header: [1, 2, 3, 4, 5, 6, false] }],
  [{ font: [] }],
  [{ size: ["small", false, "large", "huge"] }],

  // Estilos de texto
  ["bold", "italic", "underline", "strike"],
  [{ color: [] }, { background: [] }],
  [{ script: "sub" }, { script: "super" }],

  // Alinhamento e direção
  [{ align: [] }],
  [{ direction: "rtl" }],

  // Listas e indentação
  [{ list: "ordered" }, { list: "bullet" }],
  [{ indent: "-1" }, { indent: "+1" }],

  // Links, imagens e vídeos
  ["link", "image", "video"],

  // Tabelas
  ["better-table"],

  // Blocos especiais
  ["blockquote", "code-block"],

  // Limpeza de formatação
  ["clean"],
];

// Configuração dos módulos
const modules = {
  toolbar: toolbarOptions,
  "better-table": {
    operationMenu: {
      items: {
        unmergeCells: {
          text: "Another unmerge cells name",
        },
      },
      color: {
        colors: ["green", "red", "yellow", "blue", "white"],
        text: "Background Colors:",
      },
    },
  },
  keyboard: {
    bindings: QuillBetterTable.keyboardBindings,
  },
};

// Funções utilitárias para conversão
const htmlToMarkdown = (html) => {
  // Conversão básica de HTML para Markdown
  return html
    .replace(/<h([1-6])>/g, (_, level) => "#".repeat(parseInt(level)) + " ")
    .replace(/<\/h[1-6]>/g, "\n\n")
    .replace(/<strong>/g, "**")
    .replace(/<\/strong>/g, "**")
    .replace(/<b>/g, "**")
    .replace(/<\/b>/g, "**")
    .replace(/<em>/g, "*")
    .replace(/<\/em>/g, "*")
    .replace(/<i>/g, "*")
    .replace(/<\/i>/g, "*")
    .replace(/<u>/g, "_")
    .replace(/<\/u>/g, "_")
    .replace(/<blockquote>/g, "> ")
    .replace(/<\/blockquote>/g, "\n\n")
    .replace(/<code>/g, "`")
    .replace(/<\/code>/g, "`")
    .replace(/<pre><code>/g, "```\n")
    .replace(/<\/code><\/pre>/g, "\n```")
    .replace(/<a href="([^"]*)"[^>]*>([^<]*)<\/a>/g, "[$2]($1)")
    .replace(/<img[^>]*src="([^"]*)"[^>]*alt="([^"]*)"[^>]*>/g, "![$2]($1)")
    .replace(/<ul>/g, "")
    .replace(/<\/ul>/g, "\n")
    .replace(/<ol>/g, "")
    .replace(/<\/ol>/g, "\n")
    .replace(/<li>/g, "- ")
    .replace(/<\/li>/g, "\n")
    .replace(/<br\s*\/?>/g, "\n")
    .replace(/<p>/g, "")
    .replace(/<\/p>/g, "\n\n")
    .replace(/<[^>]*>/g, "") // Remove outras tags HTML
    .replace(/\n{3,}/g, "\n\n") // Remove quebras de linha excessivas
    .trim();
};

const markdownToHtml = (markdown) => {
  // Conversão básica de Markdown para HTML
  return markdown
    .replace(/^### (.*$)/gim, "<h3>$1</h3>")
    .replace(/^## (.*$)/gim, "<h2>$1</h2>")
    .replace(/^# (.*$)/gim, "<h1>$1</h1>")
    .replace(/\*\*(.*)\*\*/gim, "<strong>$1</strong>")
    .replace(/\*(.*)\*/gim, "<em>$1</em>")
    .replace(/_(.*?)_/gim, "<u>$1</u>")
    .replace(/`(.*?)`/gim, "<code>$1</code>")
    .replace(/```\n([\s\S]*?)\n```/gim, "<pre><code>$1</code></pre>")
    .replace(/> (.*$)/gim, "<blockquote>$1</blockquote>")
    .replace(/\[([^\]]*)\]\(([^)]*)\)/gim, '<a href="$2">$1</a>')
    .replace(/!\[([^\]]*)\]\(([^)]*)\)/gim, '<img alt="$1" src="$2" />')
    .replace(/^- (.*$)/gim, "<li>$1</li>")
    .replace(/(<li>.*<\/li>)/s, "<ul>$1</ul>")
    .replace(/\n/gim, "<br />");
};

// Editor is an uncontrolled React component
const Editor = forwardRef(
  ({ readOnly, defaultValue, onTextChange, onSelectionChange }, ref) => {
    const containerRef = useRef(null);
    const defaultValueRef = useRef(defaultValue);
    const onTextChangeRef = useRef(onTextChange);
    const onSelectionChangeRef = useRef(onSelectionChange);

    useLayoutEffect(() => {
      onTextChangeRef.current = onTextChange;
      onSelectionChangeRef.current = onSelectionChange;
    });

    useEffect(() => {
      ref.current?.enable(!readOnly);
    }, [ref, readOnly]);

    useEffect(() => {
      const container = containerRef.current;
      const editorContainer = container.appendChild(
        container.ownerDocument.createElement("div")
      );
      const quill = new Quill(editorContainer, {
        theme: "snow",
        modules: modules,
        formats: [
          "header",
          "font",
          "size",
          "bold",
          "italic",
          "underline",
          "strike",
          "blockquote",
          "list",
          "bullet",
          "indent",
          "link",
          "image",
          "video",
          "color",
          "background",
          "align",
          "direction",
          "code-block",
          "script",
          "better-table",
        ],
      });

      ref.current = quill;

      // Adicionar métodos utilitários ao ref
      ref.current.getHTML = () => quill.root.innerHTML;
      ref.current.setHTML = (html) => (quill.root.innerHTML = html);
      ref.current.getMarkdown = () => htmlToMarkdown(quill.root.innerHTML);
      ref.current.setMarkdown = (markdown) =>
        (quill.root.innerHTML = markdownToHtml(markdown));
      ref.current.insertTable = (rows = 3, cols = 3) => {
        const tableModule = quill.getModule("better-table");
        tableModule.insertTable(rows, cols);
      };
      ref.current.getPlainText = () => quill.getText();
      ref.current.insertHTML = (html) => {
        const range = quill.getSelection();
        if (range) {
          quill.clipboard.dangerouslyPasteHTML(range.index, html);
        } else {
          quill.clipboard.dangerouslyPasteHTML(html);
        }
      };

      if (defaultValueRef.current) {
        quill.setContents(defaultValueRef.current);
      }

      quill.on(Quill.events.TEXT_CHANGE, (...args) => {
        onTextChangeRef.current?.(...args);
      });

      quill.on(Quill.events.SELECTION_CHANGE, (...args) => {
        onSelectionChangeRef.current?.(...args);
      });

      return () => {
        ref.current = null;
        container.innerHTML = "";
      };
    }, [ref]);

    return <div ref={containerRef}></div>;
  }
);

Editor.displayName = "Editor";

// Exportar funções utilitárias
export { htmlToMarkdown, markdownToHtml };
export default Editor;
