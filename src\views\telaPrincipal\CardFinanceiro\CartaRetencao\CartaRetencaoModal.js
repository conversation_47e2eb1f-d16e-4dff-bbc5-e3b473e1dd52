import React, { useCallback, useEffect, useState } from "react";
import { CModal, CModalHeader } from "@coreui/react";
import { useMyContext } from "src/reusable/DataContext";
import CartaRetencaoBodyFooter from "./CartaRetencaoBodyFooter";
import { getApi, getApiInline } from "src/reusable/functions";
import CardLoading from "src/reusable/CardLoading";
import CartaRetencaoBensTable from "./CartaRetencaoBensTable";

const CartaRetencaoModal = ({ isOpen, onClose, datacobData }) => {
  const { userLogged } = useMyContext();
  const [loading, setLoading] = useState(false);
  const [bensData, setBensData] = useState([]);
  const [selectedBens, setSelectedBens] = useState([]);
  const [hasBensToSelect, setHasBensToSelect] = useState(false);

  const handleClose = () => {
    onClose();
  };

  const loadBens = useCallback(async () => {
    if (!datacobData || datacobData.length === 0) return;
    console.log(datacobData);

    setLoading(true);
    try {
      const response = await getApi(
        { IdContrato: datacobData[0]?.idAgrupamento },
        "getbensdatacob"
      );
      if (response && response.length > 0) {
        setBensData(response);
        setHasBensToSelect(response.length > 1);
        setSelectedBens(response[0]);
      } else {
        setHasBensToSelect(false);
      }
    } catch (error) {
      console.error("Erro ao carregar bens:", error);
      setHasBensToSelect(false);
    }
    setLoading(false);
  }, [datacobData]);

  useEffect(() => {
    if (isOpen && datacobData && datacobData.length > 0) {
      loadBens();
    }
  }, [isOpen, datacobData, loadBens]);

  return (
    <CModal
      show={isOpen}
      onClose={handleClose}
      closeOnBackdrop={false}
      className="custom-modal modal-xxl"
      centered
    >
      <CModalHeader closeButton className="my-0 pt-2 pb-0">
        <h5>Dados para solicitar Carta de Retenção:</h5>
      </CModalHeader>
      {!loading && (
        <>
          {!hasBensToSelect && (
            <CartaRetencaoBodyFooter
              onClose={handleClose}
              datacobData={datacobData}
              selectedBens={selectedBens}
            />
          )}

          {hasBensToSelect && (
            <CartaRetencaoBensTable
              bensData={bensData}
              onSelect={(bens) => {
                setHasBensToSelect(false);
                setSelectedBens(bens);
              }}
            />
          )}
        </>
      )}
      {loading && <CardLoading />}
    </CModal>
  );
};

export default CartaRetencaoModal;
