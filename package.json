{"name": "tela-unica", "version": "1.0.0", "description": "", "author": {"name": "CoreUI", "url": "https://coreui.io", "github": "https://github.com/coreui", "twitter": "https://twitter.com/core_ui"}, "contributors": [{"name": "CoreUI Team", "url": "https://github.com/orgs/coreui/people"}], "homepage": "/", "copyright": "Copyright 2017-2020 creative<PERSON><PERSON><PERSON>", "license": "MIT", "private": true, "repository": {"type": "git", "url": "**************:coreui/coreui-free-react-admin-template.git"}, "dependencies": {"@coreui/chartjs": "^2.0.0", "@coreui/coreui": "^3.4.0", "@coreui/icons": "^2.1.0", "@coreui/icons-react": "^1.1.0", "@coreui/react": "^3.4.6", "@coreui/react-chartjs": "^1.1.0", "@coreui/utils": "^1.3.1", "@microsoft/signalr": "^8.0.7", "@tinymce/tinymce-react": "^6.3.0", "@types/dom-speech-recognition": "^0.0.4", "@wojtekmaj/enzyme-adapter-react-17": "^0.6.0", "axios": "^1.6.2", "classnames": "^2.2.6", "core-js": "^3.9.1", "enzyme": "^3.11.0", "isomorphic-ws": "^5.0.0", "jssip": "^3.10.1", "jwt-decode": "^4.0.0", "prop-types": "^15.7.2", "qrcode.react": "^4.2.0", "quill": "^2.0.3", "quill-better-table": "^1.2.10", "quill-table-ui": "^1.0.7", "react": "^17.0.2", "react-app-polyfill": "^2.0.0", "react-colorful": "^5.6.1", "react-datepicker": "^4.15.0", "react-dom": "^17.0.2", "react-dropzone": "^14.2.3", "react-input-mask": "^2.0.4", "react-quill": "^2.0.0", "react-redux": "^7.2.3", "react-router-dom": "^5.2.0", "react-scroll": "^1.9.0", "react-select": "^5.7.3", "react-toastify": "^9.1.3", "redux": "^4.0.5", "sass": "^1.32.8", "websocket-as-promised": "^2.0.1", "ws": "^8.15.0", "xlsx": "^0.18.5"}, "devDependencies": {"@babel/core": "^7.13.14", "@babel/plugin-proposal-private-property-in-object": "^7.13.0", "auto-changelog": "~2.2.1", "react-app-rewired": "^2.2.1", "react-scripts": "^5.0.1", "webpack-obfuscator": "^3.5.1"}, "scripts": {"start": "react-scripts start", "build": "react-scripts build && javascript-obfuscator build/static/js --output build/static/js --compact true --self-defending true", "test": "react-scripts test", "test:cov": "npm test -- --coverage --watchAll=false", "test:debug": "react-scripts --inspect-brk test --runInBand", "eject": "react-scripts eject", "changelog": "auto-changelog --starting-version 3.0.0 --commit-limit false --hide-credit", "postbuild": "cp web.config build/"}, "bugs": {"url": "https://github.com/coreui/coreui-free-react-admin-template/issues"}, "eslintConfig": {"extends": "react-app"}, "browserslist": [">0.2%", "not dead", "not ie <= 10", "not op_mini all"], "jest": {"collectCoverageFrom": ["src/**/*.{js,jsx}", "!**/*index.js", "!src/serviceWorker.js", "!src/polyfill.js"]}, "engines": {"node": ">=10", "npm": ">=6"}}