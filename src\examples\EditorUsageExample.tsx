import React, { useRef, useState } from 'react';
import { <PERSON>utton, <PERSON>ard, CCardBody, CCardHeader, CCol, CRow } from '@coreui/react';
import Editor from '../reusable/Editor';

/**
 * Exemplo de como usar o novo Editor Quill configurado em componentes TypeScript
 */
const EditorUsageExample: React.FC = () => {
  const editorRef = useRef<any>(null);
  const [savedContent, setSavedContent] = useState<string>('');

  // Função para obter o conteúdo HTML do editor
  const getEditorHTML = () => {
    if (editorRef.current) {
      const html = editorRef.current.getHTML();
      console.log('HTML Content:', html);
      setSavedContent(html);
      return html;
    }
    return '';
  };

  // Função para definir conteúdo HTML no editor
  const setEditorHTML = (html: string) => {
    if (editorRef.current) {
      editorRef.current.setHTML(html);
    }
  };

  // Função para obter conteúdo em Markdown
  const getEditorMarkdown = () => {
    if (editorRef.current) {
      const markdown = editorRef.current.getMarkdown();
      console.log('Markdown Content:', markdown);
      return markdown;
    }
    return '';
  };

  // Função para definir conteúdo Markdown no editor
  const setEditorMarkdown = (markdown: string) => {
    if (editorRef.current) {
      editorRef.current.setMarkdown(markdown);
    }
  };

  // Função para inserir uma tabela
  const insertTable = () => {
    if (editorRef.current) {
      editorRef.current.insertTable(3, 4); // 3 linhas, 4 colunas
    }
  };

  // Função para obter texto simples
  const getPlainText = () => {
    if (editorRef.current) {
      const plainText = editorRef.current.getPlainText();
      console.log('Plain Text:', plainText);
      return plainText;
    }
    return '';
  };

  // Função para inserir HTML personalizado
  const insertCustomHTML = () => {
    const customHTML = `
      <div style="background-color: #f0f8ff; padding: 10px; border-radius: 5px; margin: 10px 0;">
        <h3 style="color: #0066cc;">Exemplo de HTML Personalizado</h3>
        <p>Este é um exemplo de como inserir <strong>HTML personalizado</strong> no editor.</p>
        <ul>
          <li>Item 1</li>
          <li>Item 2</li>
          <li>Item 3</li>
        </ul>
      </div>
    `;
    
    if (editorRef.current) {
      editorRef.current.insertHTML(customHTML);
    }
  };

  // Função para carregar conteúdo de exemplo
  const loadSampleContent = () => {
    const sampleHTML = `
      <h1>Documento de Exemplo</h1>
      <h2>Introdução</h2>
      <p>Este é um exemplo de documento criado com o <strong>Editor Quill</strong> configurado.</p>
      
      <h3>Características do Editor:</h3>
      <ul>
        <li>Formatação completa de texto</li>
        <li>Suporte para tabelas</li>
        <li>Inserção de imagens e links</li>
        <li>Conversão HTML ↔ Markdown</li>
        <li>Blocos de código e citações</li>
      </ul>

      <blockquote>
        "O editor Quill oferece uma experiência rica de edição de texto para aplicações web modernas."
      </blockquote>

      <h3>Exemplo de Código:</h3>
      <pre><code>function exemplo() {
  console.log("Olá mundo!");
  return "Editor Quill configurado!";
}</code></pre>

      <p>Para mais informações, visite <a href="https://quilljs.com">https://quilljs.com</a></p>
    `;
    
    setEditorHTML(sampleHTML);
  };

  // Função para limpar o editor
  const clearEditor = () => {
    if (editorRef.current) {
      editorRef.current.setHTML('');
      setSavedContent('');
    }
  };

  return (
    <CRow>
      <CCol xs={12}>
        <CCard>
          <CCardHeader>
            <h4>Exemplo de Uso do Editor Quill</h4>
            <p className="text-muted mb-0">
              Demonstração das funcionalidades do editor configurado
            </p>
          </CCardHeader>
          <CCardBody>
            {/* Botões de controle */}
            <div className="mb-3">
              <CButton 
                color="primary" 
                size="sm" 
                className="me-2 mb-2"
                onClick={loadSampleContent}
              >
                Carregar Exemplo
              </CButton>
              <CButton 
                color="success" 
                size="sm" 
                className="me-2 mb-2"
                onClick={getEditorHTML}
              >
                Obter HTML
              </CButton>
              <CButton 
                color="info" 
                size="sm" 
                className="me-2 mb-2"
                onClick={getEditorMarkdown}
              >
                Obter Markdown
              </CButton>
              <CButton 
                color="secondary" 
                size="sm" 
                className="me-2 mb-2"
                onClick={insertTable}
              >
                Inserir Tabela
              </CButton>
              <CButton 
                color="warning" 
                size="sm" 
                className="me-2 mb-2"
                onClick={insertCustomHTML}
              >
                Inserir HTML
              </CButton>
              <CButton 
                color="dark" 
                size="sm" 
                className="me-2 mb-2"
                onClick={getPlainText}
              >
                Texto Simples
              </CButton>
              <CButton 
                color="danger" 
                size="sm" 
                className="mb-2"
                onClick={clearEditor}
              >
                Limpar
              </CButton>
            </div>

            {/* Editor */}
            <div style={{ minHeight: '400px', border: '1px solid #e0e6ed', borderRadius: '6px' }}>
              <Editor ref={editorRef} />
            </div>

            {/* Visualização do conteúdo salvo */}
            {savedContent && (
              <div className="mt-4">
                <h5>Conteúdo HTML Capturado:</h5>
                <div className="bg-light p-3 rounded" style={{ maxHeight: '200px', overflow: 'auto' }}>
                  <pre style={{ fontSize: '12px', margin: 0 }}>
                    {savedContent}
                  </pre>
                </div>
              </div>
            )}

            {/* Instruções de uso */}
            <div className="mt-4">
              <h5>Como usar em seus componentes:</h5>
              <div className="bg-light p-3 rounded">
                <pre style={{ fontSize: '12px', margin: 0 }}>
{`// 1. Importar o Editor
import Editor from '../reusable/Editor';

// 2. Criar uma ref
const editorRef = useRef<any>(null);

// 3. Usar o componente
<Editor ref={editorRef} />

// 4. Acessar métodos via ref
const html = editorRef.current?.getHTML();
const markdown = editorRef.current?.getMarkdown();
editorRef.current?.setHTML('<p>Novo conteúdo</p>');
editorRef.current?.insertTable(3, 4);`}
                </pre>
              </div>
            </div>
          </CCardBody>
        </CCard>
      </CCol>
    </CRow>
  );
};

export default EditorUsageExample;
