import React, { useCallback, useEffect, useState } from "react";
import {
  <PERSON>utton,
  CCard,
  CCardBody,
  CInput,
  CModal,
  CModalBody,
  CModalFooter,
  CModalHeader,
} from "@coreui/react";
import { getApi, postApi, postApiQueryFile } from "src/reusable/functions";
import CardLoading from "src/reusable/CardLoading";
import Select from "react-select";
import {
  convertCurrencyToFloatDynamic,
  formatCurrency,
} from "src/reusable/helpers";
import { toast } from "react-toastify";

const CartaRetencaoBodyFooter = ({ onClose, datacobData, selectedBens }) => {
  const user = localStorage.getItem("user")
    ? JSON.parse(localStorage.getItem("user"))
    : null;

  const financiadoData = localStorage.getItem("financiadoData")
    ? JSON.parse(localStorage.getItem("financiadoData"))
    : null;

  const [loading, setLoading] = useState(false);
  const [generateLoading, setGenerateLoading] = useState(false);
  const [previewLoading, setPreviewLoading] = useState(false);
  const [previewUrl, setPreviewUrl] = useState(null);
  const [showPreview, setShowPreview] = useState(false);

  // Estados do formulário
  const [contrato, setContrato] = useState(null);
  const [tipoCartaRetencaoSelected, setTipoCartaRetencaoSelected] =
    useState("");
  const [tiposCartaRetencao, setTiposCartaRetencao] = useState([]);
  const [adversoPrincipal, setAdversoPrincipal] = useState(
    financiadoData?.nome || ""
  );
  const [grupoCota, setGrupoCota] = useState("");
  const [clientePrincipal, setClientePrincipal] = useState(
    financiadoData?.cliente?.trim() || ""
  );
  const [parcelaProrrogada, setParcelaProrrogada] = useState("");
  const [parcelaRateio, setParcelaRateio] = useState("");
  const [parcelasARatear, setParcelasARatear] = useState("");

  const [bemAtual, setBemAtual] = useState(
    new Set([selectedBens?.marca || "", selectedBens?.modelo || ""]).join(" - ")
  );

  const [valorBemAtual, setValorBemAtual] = useState(
    selectedBens?.vl_Venda || 0
  );
  const [novoBem, setNovoBem] = useState("");
  const [valorNovoBem, setValorNovoBem] = useState(0);
  const [valorParcela, setValorParcela] = useState(0);
  const [dataProcesso, setDataProcesso] = useState("");

  const handleSelect = (option) => {
    setContrato(option);
    if (option) {
      setGrupoCota(option.nrContrato || "");
    }
  };

  const loadTiposCartaRetencao = useCallback(async () => {
    setLoading(true);
    try {
      const response = await getApi({}, "getTipoCartaRetencao");
      if (response && response.length > 0) {
        setTiposCartaRetencao(response);
        // Selecionar o primeiro tipo por padrão
        setTipoCartaRetencaoSelected(response[0]?.id || "");
      }
    } catch (error) {
      console.error("Erro ao carregar tipos de carta de retenção:", error);
    }
    setLoading(false);
  }, []);

  useEffect(() => {
    Promise.all([loadTiposCartaRetencao()]);
  }, [loadTiposCartaRetencao]);

  const handleClosePreview = () => {
    setShowPreview(false);
    if (previewUrl) {
      URL.revokeObjectURL(previewUrl);
      setPreviewUrl(null);
    }
  };

  const handlePreview = async () => {
    if (!contrato?.idContrato) {
      toast.error("Selecione um contrato primeiro");
      return;
    }

    setPreviewLoading(true);
    try {
      const previewResponse = await postApiQueryFile(
        "postPedidoCartasEtermosGenerateCartaRetencaoPreview",
        "",
        {
          crm: user?.activeConnection,
          idGrupo: contrato?.idGrupo,
          tipoCartaRetencaoId: tipoCartaRetencaoSelected,
          adversoPrincipal: adversoPrincipal,
          grupoCota: grupoCota,
          clientePrincipal: clientePrincipal,
          parcelaProrrogada: parcelaProrrogada,
          parcelaRateio: parcelaRateio,
          parcelasARatear: parcelasARatear,
          bemAtual: bemAtual,
          valorBemAtual: formatCurrency(valorBemAtual, true),
          novoBem: novoBem,
          valorNovoBem: formatCurrency(valorNovoBem, true),
          valorParcela: formatCurrency(valorParcela, true),
          dataProcesso: dataProcesso,
        }
      );

      if (previewResponse.ok) {
        const blob = await previewResponse.blob();
        if (blob.type === "application/pdf" || blob.size > 0) {
          const url = URL.createObjectURL(blob);
          setPreviewUrl(url);
          setShowPreview(true);
        } else {
          toast.error("Não foi possível gerar o preview do documento");
        }
      } else {
        toast.error("Erro ao gerar preview do documento");
      }
    } catch (error) {
      console.error("Erro ao gerar preview:", error);
      toast.error("Erro ao gerar preview do documento");
    }
    setPreviewLoading(false);
  };

  const handleGenerate = async () => {
    if (!contrato?.idContrato) {
      toast.error("Selecione um contrato primeiro");
      return;
    }

    if (!tipoCartaRetencaoSelected) {
      toast.error("Selecione um tipo de carta de retenção");
      return;
    }

    setGenerateLoading(true);
    try {
      const response = await postApi(
        {
          crm: user?.activeConnection,
          idGrupo: contrato?.idGrupo,
          tipo: 1,
          idContrato: contrato?.idContrato,
          idFinanciado: financiadoData?.id_Financiado,
          idOperador: user?.id,
        },
        "postPedidoCartasEtermos"
      );

      if (response?.success === true && response?.data?.id !== undefined) {
        const infos = await postApi(
          {
            pedidoId: response.data.id,
            tipoCartaRetencaoId: tipoCartaRetencaoSelected,
            adversoPrincipal: adversoPrincipal?.trim(),
            grupoCota: grupoCota,
            clientePrincipal: clientePrincipal,
            parcelaProrrogada: parcelaProrrogada,
            parcelaRateio: parcelaRateio,
            parcelasARatear: parcelasARatear,
            bemAtual: bemAtual,
            valorBemAtual: formatCurrency(valorBemAtual, true),
            novoBem: novoBem,
            valorNovoBem: formatCurrency(valorNovoBem, true),
            valorParcela: formatCurrency(valorParcela, true),
            dataProcesso: dataProcesso,
          },
          "postCartaRetencaoInfos"
        );
        if (infos?.success === true && infos?.data === true) {
          toast.success("Requisição enviada com sucesso!");
          onClose();
        } else {
          toast.error("Erro ao enviar informação da requisição!");
        }
      } else {
        toast.error("Erro ao enviar requisição!");
      }
    } catch (error) {
      console.error(error);
      toast.error("Erro ao enviar requisição!");
    }
    setGenerateLoading(false);
  };

  if (loading) {
    return <CardLoading />;
  }

  const handleInputFloatChange = (e, setFunction) => {
    setFunction(convertCurrencyToFloatDynamic(e.target.value) || 0);
  };

  const handleInputTextChange = (e, setFunction) => {
    setFunction(e.target.value);
  };

  return (
    <>
      <CModalBody>
        <CCard>
          <CCardBody>
            <div className="row mt-0">
              <div className="col-md-4">
                <label className="pt-1">Selecione o Contrato:</label>
                <Select
                  className="mr-2 ml-2"
                  options={datacobData}
                  getOptionLabel={(option) => option?.nrContrato}
                  getOptionValue={(option) => option?.nrContrato}
                  onChange={(option) => handleSelect(option)}
                  placeholder="Selecione"
                />
              </div>
              <div className="col-md-4">
                <label className="pt-1">Tipo de Acordo:</label>
                <Select
                  className="mr-2 ml-2"
                  options={tiposCartaRetencao}
                  getOptionLabel={(option) => option?.nome}
                  getOptionValue={(option) => option?.id}
                  value={tiposCartaRetencao.find(
                    (t) => t.id === tipoCartaRetencaoSelected
                  )}
                  onChange={(option) =>
                    setTipoCartaRetencaoSelected(option?.value || "")
                  }
                  placeholder="Selecione o tipo de acordo"
                />
              </div>
            </div>

            <div className="row mt-4">
              <div className="col-md-3">
                <label className="pt-1">Nome do Cliente:</label>
                <CInput
                  className="mr-2 ml-2"
                  value={adversoPrincipal}
                  onChange={(e) =>
                    handleInputTextChange(e, setAdversoPrincipal)
                  }
                  placeholder="Nome do cliente"
                />
              </div>
              <div className="col-md-3">
                <label className="pt-1">Grupo e Cota:</label>
                <CInput
                  className="mr-2 ml-2"
                  value={grupoCota}
                  onChange={(e) => handleInputTextChange(e, setGrupoCota)}
                  placeholder="Grupo e Cota"
                />
              </div>
              <div className="col-md-3">
                <label className="pt-1">Administradora de Consórcio:</label>
                <CInput
                  className="mr-2 ml-2"
                  value={clientePrincipal}
                  onChange={(e) =>
                    handleInputTextChange(e, setClientePrincipal)
                  }
                  placeholder="Administradora de Consórcio"
                />
              </div>
              <div className="col-md-3">
                <label className="pt-1">Parcela Prorrogada:</label>
                <CInput
                  className="mr-2 ml-2"
                  value={parcelaProrrogada}
                  onChange={(e) =>
                    handleInputTextChange(e, setParcelaProrrogada)
                  }
                  placeholder="Parcela Prorrogada"
                />
              </div>
            </div>

            <div className="row mt-4">
              <div className="col-md-3">
                <label className="pt-1">Parcela Rateio:</label>
                <CInput
                  className="mr-2 ml-2"
                  value={parcelaRateio}
                  onChange={(e) => handleInputTextChange(e, setParcelaRateio)}
                  placeholder="Parcela Rateio"
                />
              </div>
              <div className="col-md-3">
                <label className="pt-1">Parcelas a Ratear:</label>
                <CInput
                  className="mr-2 ml-2"
                  value={parcelasARatear}
                  onChange={(e) => handleInputTextChange(e, setParcelasARatear)}
                  placeholder="Parcelas a Ratear"
                />
              </div>
              <div className="col-md-3">
                <label className="pt-1">Bem Atual:</label>
                <CInput
                  className="mr-2 ml-2"
                  value={bemAtual}
                  onChange={(e) => handleInputTextChange(e, setBemAtual)}
                  placeholder="Bem Atual"
                />
              </div>
              <div className="col-md-3">
                <label className="pt-1">Valor Bem Atual:</label>
                <CInput
                  className="mr-2 ml-2"
                  value={formatCurrency(valorBemAtual, false)}
                  onChange={(e) => handleInputFloatChange(e, setValorBemAtual)}
                  placeholder="Valor Bem Atual"
                />
              </div>
            </div>

            <div className="row mt-4">
              <div className="col-md-3">
                <label className="pt-1">Novo Bem:</label>
                <CInput
                  className="mr-2 ml-2"
                  value={novoBem}
                  onChange={(e) => handleInputTextChange(e, setNovoBem)}
                  placeholder="Novo Bem"
                />
              </div>
              <div className="col-md-3">
                <label className="pt-1">Valor Novo Bem:</label>
                <CInput
                  className="mr-2 ml-2"
                  value={formatCurrency(valorNovoBem, false)}
                  onChange={(e) => handleInputFloatChange(e, setValorNovoBem)}
                  placeholder="Valor Novo Bem"
                />
              </div>
              <div className="col-md-3">
                <label className="pt-1">Valor Parcela:</label>
                <CInput
                  className="mr-2 ml-2"
                  value={formatCurrency(valorParcela, false)}
                  onChange={(e) => handleInputFloatChange(e, setValorParcela)}
                  placeholder="Valor Parcela"
                />
              </div>
              <div className="col-md-3">
                <label className="pt-1">Data do Processo:</label>
                <CInput
                  className="mr-2 ml-2"
                  type="date"
                  value={dataProcesso}
                  onChange={(e) => handleInputTextChange(e, setDataProcesso)}
                  placeholder="Data do Processo"
                />
              </div>
            </div>
          </CCardBody>
        </CCard>
      </CModalBody>

      <CModalFooter>
        <CButton color="danger" className="mr-2" onClick={onClose}>
          Sair
        </CButton>
        <CButton
          color="warning"
          className="mr-2 text-white"
          onClick={handlePreview}
          disabled={previewLoading}
        >
          {previewLoading ? "Gerando..." : "Visualizar Prévia da Carta"}
        </CButton>
        <CButton
          color="success"
          className="mr-2"
          onClick={handleGenerate}
          disabled={generateLoading}
        >
          {generateLoading ? "Gravando..." : "Gravar Carta"}
        </CButton>
      </CModalFooter>

      {showPreview && (
        <CModal
          show={showPreview}
          onClose={handleClosePreview}
          closeOnBackdrop={false}
          className="preview-modal"
          centered
        >
          <CModalHeader closeButton>
            <h5>Preview da Carta de Retenção</h5>
          </CModalHeader>
          <CModalBody style={{ padding: "0", height: "80vh" }}>
            {previewUrl && (
              <iframe
                src={previewUrl}
                style={{
                  width: "100%",
                  height: "100%",
                  border: "none",
                }}
                title="Preview da Carta de Retenção"
              />
            )}
          </CModalBody>
          <CModalFooter>
            <CButton color="secondary" onClick={handleClosePreview}>
              Fechar Preview
            </CButton>
          </CModalFooter>
        </CModal>
      )}
    </>
  );
};

export default CartaRetencaoBodyFooter;
