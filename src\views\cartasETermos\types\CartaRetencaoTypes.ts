export interface CartaRetencaoInfosType {
  id: string;
  tipoCartaRetencaoId: string;
  pedidoId: string;
  nomeCliente: string;
  grupoCota: string;
  administradoraConsorcio: string;
  tipoAcordo: string;
  parcelaProrrogada: string;
  parcelaRateio: string;
  parcelasARatear: string;
  bemAtual: string;
  valorBemAtual: number;
  novoBem: string;
  valorNovoBem: number;
  valorParcela: number;
  dataProcesso: string; // ISO date string (YYYY-MM-DD)
  createdAt: string; // ISO datetime string
  extras: string;
}

export interface CartaRetencaoApiResponse {
  success: boolean;
  data: CartaRetencaoInfosType;
  message?: string;
}

export interface TipoCartaRetencao {
  id: string;
  nome: string;
  descricao: string;
  ativo: boolean;
}

export interface ConteudoCartaRetencaoType {
  nome: string;
  crm: string;
  grupoId: number | null;
}

// Enum para tipos de acordo
export enum TipoAcordoCartaRetencao {
  RATEIO_100 = "Rateio 100%",
  PRORROGACAO = "Prorrogação",
  PRORROGACAO_RATEIO_50 = "Prorrogação - Rateio 50%",
  PRORROGACAO_TROCA_BEM = "Prorrogação - Troca de Bem",
  RATEIO_50 = "Rateio - 50%",
  TROCA_BEM = "Troca de Bem"
}
