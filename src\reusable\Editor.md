# Editor Quill Completo

Este é um componente de editor de texto rico baseado no Quill.js com funcionalidades completas para edição de texto, incluindo suporte para HTML e Markdown.

## Características

### Toolbar Completa
- **Formatação de texto**: Headers (H1-H6), fontes, tamanhos
- **Estilos**: Negrito, itálico, sublinhado, tachado
- **Cores**: Cor do texto e cor de fundo
- **Alinhamento**: Esquerda, centro, direita, justificado
- **Listas**: Ordenadas e não ordenadas
- **Indentação**: Aumentar/diminuir indentação
- **Links, imagens e vídeos**
- **Tabelas**: Com funcionalidades avançadas
- **Blocos especiais**: Citações e blocos de código
- **Subscrito e sobrescrito**
- **Direção do texto**: RTL (Right-to-Left)
- **Limpeza de formatação**

### Funcionalidades Avançadas
- **Conversão HTML ↔ Markdown**
- **Inserção de tabelas interativas**
- **Inserção de HTML personalizado**
- **Extração de texto simples**
- **Suporte completo para formatação**

## Como Usar

### Uso Básico

```jsx
import React, { useRef } from 'react';
import Editor from '../reusable/Editor';

const MyComponent = () => {
  const editorRef = useRef(null);

  const handleTextChange = () => {
    console.log('Texto alterado');
  };

  return (
    <Editor
      ref={editorRef}
      onTextChange={handleTextChange}
      defaultValue=""
      readOnly={false}
    />
  );
};
```

### Props Disponíveis

- `readOnly` (boolean): Define se o editor é somente leitura
- `defaultValue` (string): Valor inicial do editor
- `onTextChange` (function): Callback chamado quando o texto muda
- `onSelectionChange` (function): Callback chamado quando a seleção muda

### Métodos Disponíveis via Ref

```jsx
// Obter/definir conteúdo HTML
const html = editorRef.current.getHTML();
editorRef.current.setHTML('<p>Novo conteúdo</p>');

// Obter/definir conteúdo Markdown
const markdown = editorRef.current.getMarkdown();
editorRef.current.setMarkdown('# Título\n\nParágrafo');

// Inserir tabela
editorRef.current.insertTable(3, 4); // 3 linhas, 4 colunas

// Obter texto simples
const plainText = editorRef.current.getPlainText();

// Inserir HTML personalizado
editorRef.current.insertHTML('<strong>Texto em negrito</strong>');
```

### Funções Utilitárias

```jsx
import { htmlToMarkdown, markdownToHtml } from '../reusable/Editor';

// Converter HTML para Markdown
const markdown = htmlToMarkdown('<h1>Título</h1><p>Parágrafo</p>');

// Converter Markdown para HTML
const html = markdownToHtml('# Título\n\nParágrafo');
```

## Exemplo Completo

Veja o arquivo `src/examples/EditorExample.js` para um exemplo completo de uso com todas as funcionalidades.

## Dependências

O editor utiliza as seguintes dependências que já estão instaladas no projeto:

- `quill`: ^2.0.3
- `react-quill`: ^2.0.0
- `quill-better-table`: ^1.2.10

## Funcionalidades de Tabela

O editor inclui funcionalidades avançadas de tabela através do módulo `quill-better-table`:

- Inserção de tabelas
- Adição/remoção de linhas e colunas
- Mesclagem de células
- Cores de fundo personalizadas
- Menu contextual para operações de tabela

## Suporte a Markdown

O editor inclui conversores básicos para Markdown que suportam:

- Headers (H1-H6)
- Texto em negrito e itálico
- Links e imagens
- Listas
- Citações
- Blocos de código
- Sublinhado

## Personalização

Para personalizar ainda mais o editor, você pode:

1. Modificar a configuração `toolbarOptions` no arquivo `Editor.js`
2. Adicionar novos módulos do Quill
3. Personalizar os estilos CSS
4. Estender as funções de conversão HTML/Markdown

## Notas Importantes

- O editor é um componente não controlado (uncontrolled)
- Use as funções do ref para interagir programaticamente com o editor
- As conversões HTML/Markdown são básicas e podem precisar de ajustes para casos específicos
- Para funcionalidades mais avançadas de Markdown, considere usar bibliotecas especializadas
